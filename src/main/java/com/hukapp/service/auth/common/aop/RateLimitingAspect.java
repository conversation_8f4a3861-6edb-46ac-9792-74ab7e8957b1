package com.hukapp.service.auth.common.aop;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * AOP Aspect for IP-based rate limiting on API endpoints.
 * 
 * This aspect applies rate limiting to all controller methods and tracks
 * request counts per IP address within a time window.
 * 
 * Default configuration:
 * - 100 requests per IP address per minute
 * - Automatic cleanup of expired entries every 2 minutes
 * - Returns HTTP 429 (Too Many Requests) when limit is exceeded
 */
@Aspect
@Component
@Slf4j
public class RateLimitingAspect {

    // Default rate limit config
    private static final int DEFAULT_REQUEST_LIMIT = 120;
    private static final long TIME_WINDOW_MINUTES = 1;
    
    // Thread-safe map to store IP request counts
    private final Map<String, RateLimitEntry> rateLimitCache = new ConcurrentHashMap<>();
    
    /**
     * Pointcut for all REST controller methods
     */
    @Pointcut("within(@org.springframework.web.bind.annotation.RestController *)")
    public void controllerMethods() {}
    
    /**
     * Around advice that applies rate limiting to all controller methods
     */
    @Around("controllerMethods()")
    public Object applyRateLimit(ProceedingJoinPoint joinPoint) throws Throwable {
        HttpServletRequest request = getCurrentHttpRequest();
        
        if (request == null) {
            // If we can't get the request, proceed without rate limiting
            return joinPoint.proceed();
        }

        // if it is swagger-ui or v3/api-docs, do not apply rate limiting
        if (request.getRequestURI().contains("swagger-ui") || request.getRequestURI().contains("v3/api-docs")) {
            return joinPoint.proceed();
        }
        
        String clientIp = getClientIpAddress(request);

        log.debug("Rate limit check for IP: {} on endpoint: {}", clientIp, request.getRequestURI());
        
        if (isRateLimitExceeded(clientIp)) {
            log.warn("Rate limit exceeded for IP: {} on endpoint: {}", 
                    clientIp, request.getRequestURI());
            
            return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS)
                    .body(Map.of(
                            "error", "Rate limit exceeded",
                            "message", "Too many requests from this IP address. Please try again later.",
                            "status", 429
                    ));
        }
        
        // Proceed with the original method execution
        return joinPoint.proceed();
    }
    
    /**
     * Check if the rate limit is exceeded for the given IP address
     */
    private boolean isRateLimitExceeded(String clientIp) {
        Instant now = Instant.now();
        Instant windowStart = now.minus(TIME_WINDOW_MINUTES, ChronoUnit.MINUTES);
        
        RateLimitEntry entry = rateLimitCache.computeIfAbsent(clientIp, 
                k -> new RateLimitEntry(0, now));
        
        // Reset counter if the time window has passed
        if (entry.getLastRequestTime().isBefore(windowStart)) {
            entry.setRequestCount(1);
            entry.setLastRequestTime(now);
            return false;
        }
        
        // Increment request count
        int currentCount = entry.incrementRequestCount();
        
        boolean limitExceeded = currentCount > DEFAULT_REQUEST_LIMIT;
        
        if (limitExceeded) {
            log.debug("Rate limit check for IP {}: {}/{} requests in current window", 
                    clientIp, currentCount, DEFAULT_REQUEST_LIMIT);
        } else{
            entry.setLastRequestTime(now);
        }
        
        return limitExceeded;
    }
    
    /**
     * Get the current HTTP request from the request context
     */
    private HttpServletRequest getCurrentHttpRequest() {
        try {
            ServletRequestAttributes attributes = 
                    (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
            return attributes.getRequest();
        } catch (Exception e) {
            log.debug("Could not retrieve current HTTP request: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * Extract the client IP address from the request, considering proxy headers
     */
    private String getClientIpAddress(HttpServletRequest request) {
        // Check for IP address from various proxy headers
        String[] headerNames = {
                "X-Forwarded-For",
                "X-Real-IP", 
                "X-Originating-IP",
                "CF-Connecting-IP",
                "Proxy-Client-IP",
                "WL-Proxy-Client-IP"
        };
        
        for (String headerName : headerNames) {
            String ip = request.getHeader(headerName);
            if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
                // X-Forwarded-For can contain multiple IPs, take the first one
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                return ip;
            }
        }
        
        // Fall back to remote address
        return request.getRemoteAddr();
    }
    
    /**
     * Scheduled cleanup task that runs every 2 minutes to remove expired entries
     */
    @Scheduled(fixedRate = 1000) // 2 minutes in milliseconds
    public void cleanupExpiredEntries() {
        Instant cutoff = Instant.now().minus(TIME_WINDOW_MINUTES, ChronoUnit.MINUTES);
        int initialSize = rateLimitCache.size();
        
        rateLimitCache.entrySet().removeIf(entry -> {
            boolean expired = entry.getValue().getLastRequestTime().isBefore(cutoff);
            if (expired) {
                log.debug("Removing expired rate limit entry for IP: {}", entry.getKey());
            }
            return expired;
        });
        
        int removedCount = initialSize - rateLimitCache.size();
        if (removedCount > 0) {
            log.info("Cleaned up {} expired rate limit entries. Current cache size: {}", 
                    removedCount, rateLimitCache.size());
        }
    }
    
    /**
     * Get current cache statistics (useful for monitoring)
     */
    public Map<String, Object> getCacheStatistics() {
        return Map.of(
                "totalIPs", rateLimitCache.size(),
                "cacheEntries", rateLimitCache.keySet()
        );
    }
    
    /**
     * Clear all rate limit entries (useful for testing or admin operations)
     */
    public void clearCache() {
        rateLimitCache.clear();
        log.info("Rate limit cache cleared");
    }
    
    /**
     * Inner class to represent a rate limit entry for an IP address
     */
    private static class RateLimitEntry {
        private volatile int requestCount;
        private volatile Instant lastRequestTime;
        
        public RateLimitEntry(int requestCount, Instant lastRequestTime) {
            this.requestCount = requestCount;
            this.lastRequestTime = lastRequestTime;
        }
        
        public synchronized int incrementRequestCount() {
            return ++requestCount;
        }
        
        public int getRequestCount() {
            return requestCount;
        }
        
        public void setRequestCount(int requestCount) {
            this.requestCount = requestCount;
        }
        
        public Instant getLastRequestTime() {
            return lastRequestTime;
        }
        
        public void setLastRequestTime(Instant lastRequestTime) {
            this.lastRequestTime = lastRequestTime;
        }
    }
}
